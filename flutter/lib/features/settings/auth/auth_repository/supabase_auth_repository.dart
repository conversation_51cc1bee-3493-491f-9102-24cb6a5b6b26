import 'package:flutter/foundation.dart';
import 'package:supabase_flutter/supabase_flutter.dart' as sb;
import 'package:luxury_app/shared/utils/password_generator.dart';
import 'package:luxury_app/shared/utils/keychain_helper.dart';

import '../../../../core/services/cache_service.dart';
import '../../../../core/services/supabase_service.dart';
import '../auth_models/auth_tokens.dart';
import '../auth_models/user.dart';
import 'auth_cache_service.dart';
import 'auth_repository.dart';

/// Реализация AuthRepository с использованием Supabase
class SupabaseAuthRepository implements AuthRepository {
  static SupabaseAuthRepository? _instance;
  static SupabaseAuthRepository get instance => _instance!;

  final SupabaseService supabaseService;
  final AuthCacheService _authCacheService;
  final CacheService _cacheService;

  SupabaseAuthRepository(
    this.supabaseService,
    this._authCacheService,
    this._cacheService,
  );

  @override
  Future<void> loadSavedToken() async {
    try {
      // В Supabase токены управляются автоматически
      // Проверяем есть ли активная сессия
      final session = supabaseService.currentSession;

      if (session != null && session.accessToken.isNotEmpty) {
        if (kDebugMode) {
          debugPrint('✅ Найдена активная сессия Supabase');
        }

        // Сохраняем email в кэш для совместимости
        final user = supabaseService.currentUser;
        if (user?.email != null) {
          await _authCacheService.saveUserEmail(user!.email!);
        }
      }
    } catch (e) {
      if (kDebugMode) {
        debugPrint('❌ Ошибка загрузки сохраненного токена: $e');
      }
    }
  }

  @override
  Future<User> register(String email, String password, {String? phone}) async {
    // Генерируем пароль, если пустой
    final String actualPassword = (password.isEmpty)
        ? PasswordGenerator.generate(length: 16, withSymbols: true)
        : password;

    try {
      final response = await supabaseService.signUp(
        email: email,
        password: actualPassword,
      );

      if (response.user == null) {
        throw Exception(
          'Не удалось создать пользователя. Проверьте email на подтверждение.',
        );
      }

      // Сохраняем email в кэш
      await _authCacheService.saveUserEmail(email);

      // Сохраняем пароль в Keychain/Keystore
      await KeychainHelper.savePassword(email, actualPassword);

      // Очищаем кэш чатов при новой регистрации
      await _cacheService.clearAllChatsData();

      return _supabaseUserToAppUser(response.user!);
    } catch (e) {
      if (kDebugMode) {
        debugPrint('❌ Ошибка API: $e');
      }
      rethrow;
    }
  }

  @override
  Future<AuthTokens> login(
    String email,
    String password, {
    bool rememberMe = true,
  }) async {
    try {
      final response = await supabaseService.signIn(
        email: email,
        password: password,
      );

      if (response.session == null) {
        throw Exception('Не удалось получить сессию после входа');
      }

      // Сохраняем email в кэш
      await _authCacheService.saveUserEmail(email);

      // Очищаем кэш чатов при новой авторизации
      // чтобы избежать отображения чатов другого пользователя
      await _cacheService.clearAllChatsData();

      return AuthTokens(
        accessToken: response.session!.accessToken,
        tokenType: 'Bearer',
        expiresIn: response.session!.expiresIn,
      );
    } catch (e) {
      if (kDebugMode) {
        debugPrint('❌ Ошибка API: $e');
      }
      rethrow;
    }
  }

  @override
  Future<void> logout() async {
    await supabaseService.signOut();
    await _authCacheService.clearAuthData();
    // Очищаем также кэш чатов
    await _cacheService.clearAllChatsData();
  }

  @override
  Future<bool> isAuthenticated() async {
    try {
      final session = supabaseService.currentSession;
      if (session == null || session.accessToken.isEmpty) {
        return false;
      }
      
      // Проверяем, не истёк ли токен
      final now = DateTime.now().millisecondsSinceEpoch / 1000;
      if (session.expiresAt != null && session.expiresAt! <= now) {
        return false;
      }
      
      return true;
    } catch (e) {
      return false;
    }
  }

  @override
  Future<User?> getCurrentUser() async {
    try {
      final supabaseUser = supabaseService.currentUser;

      if (supabaseUser == null) {
        // Проверяем, есть ли вообще валидная сессия
        final session = supabaseService.currentSession;
        if (session == null || session.accessToken.isEmpty) {
          // Нет сессии - можно очистить данные
          await logout();
        }
        return null;
      }

      return _supabaseUserToAppUser(supabaseUser);
    } catch (e) {
      if (kDebugMode) {
        debugPrint('❌ Ошибка получения текущего пользователя: $e');
      }
      // При ошибке также очищаем данные
      await logout();
      return null;
    }
  }

  @override
  Future<String?> getAccessToken() async {
    final session = supabaseService.currentSession;
    return session?.accessToken;
  }

  /// Сброс пароля
  @override
  Future<void> resetPassword({required String email}) async {
    try {
      await supabaseService.resetPassword(email: email);
    } catch (e) {
      if (kDebugMode) {
        debugPrint('❌ Ошибка API: $e');
      }
      rethrow;
    }
  }

  /// Подписка на изменения состояния аутентификации
  Stream<sb.AuthState> get authStateChanges =>
      supabaseService.authStateChanges;

  /// Подтверждение сброса пароля по токену
  Future<void> verifyPasswordReset({required String email, required String token, required String newPassword}) async {
    try {
      await supabaseService.verifyPasswordReset(
        email: email,
        token: token,
        newPassword: newPassword,
      );
    } catch (e) {
      if (kDebugMode) {
        debugPrint('❌ Ошибка подтверждения сброса пароля: $e');
      }
      rethrow;
    }
  }

  /// Преобразование Supabase User в модель приложения
  User _supabaseUserToAppUser(sb.User supabaseUser) {
    return User(
      id: supabaseUser.id,
      email: supabaseUser.email ?? '',
      phone: supabaseUser.phone,
      role: supabaseUser.userMetadata?['role'] as String? ?? 'user',
      createdAt: DateTime.parse(supabaseUser.createdAt),
      metadata: supabaseUser.userMetadata ?? {},
    );
  }
}
