import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:lucide_icons/lucide_icons.dart';
import 'package:go_router/go_router.dart';
import 'package:luxury_app/features/settings/auth/auth_provider.dart';
import 'package:luxury_app/features/settings/cache_info_widget.dart';
import 'package:luxury_app/features/settings/theme_selector.dart';
import 'package:luxury_app/shared/constants/sizes.dart';
import 'package:luxury_app/shared/widgets/app_buttons.dart';

class SettingsScreen extends ConsumerStatefulWidget {
  const SettingsScreen({super.key});

  @override
  ConsumerState<SettingsScreen> createState() => _SettingsScreenState();
}

class _SettingsScreenState extends ConsumerState<SettingsScreen> {
  Future<void> _handleLogout(WidgetRef ref) async {
    await ref.read(authProvider.notifier).logout();
    if (mounted) {
      context.go('/auth');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          'Настройки',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
            fontSize: 16,
          ),
          overflow: TextOverflow.ellipsis,
          maxLines: 1,
        ),
        backgroundColor: Theme.of(context).scaffoldBackgroundColor,
      ),
      body: ListView(
        padding: EdgeInsets.all(AppSizes.paddingM),
        children: [
          Center(child: const ThemeSelector()),
          SizedBox(height: AppSizes.paddingL),
          _buildUserSection(),
          SizedBox(height: AppSizes.paddingL),
          Card(
            elevation: 2,
            surfaceTintColor: Theme.of(context).colorScheme.surface,
            child: Padding(
              padding: EdgeInsets.all(AppSizes.paddingM),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Данные приложения',
                    style: Theme.of(context).textTheme.titleMedium,
                  ),
                  const SizedBox(height: 8),
                  const CacheInfoWidget(),
                ],
              ),
            ),
          ),
          SizedBox(height: AppSizes.paddingM),
        ],
      ),
    );
  }

  Widget _buildUserSection() {
    return Consumer(
      builder: (context, ref, child) {
        final authState = ref.watch(authProvider);
        
        if (!authState.isAuthenticated || authState.user == null) {
          return const SizedBox.shrink();
        }

        return Card(
          elevation: 4,
          surfaceTintColor: Theme.of(context).colorScheme.surface,
          child: Padding(
            padding: EdgeInsets.all(AppSizes.paddingM),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Аккаунт',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 16),
                _buildUserInfo(authState),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildUserInfo(AuthState authState) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        ListTile(
          contentPadding: EdgeInsets.zero,
          leading: Container(
            width: 48,
            height: 48,
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(24),
            ),
            child: Icon(
              LucideIcons.user,
              color: Theme.of(context).colorScheme.primary,
            ),
          ),
          title: Text(
            authState.user!.email,
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          subtitle: authState.user!.phone != null
              ? Text('Телефон: ${authState.user!.phone}')
              : null,
        ),
        const SizedBox(height: 16),
        SizedBox(
          width: double.infinity,
          child: AppButton(
            text: 'Выйти из аккаунта',
            onPressed: () {
              _showLogoutDialog();
            },
            icon: LucideIcons.logOut,
            style: AppButtonStyle.secondary,
          ),
        ),
      ],
    );
  }

  void _showLogoutDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Выход из аккаунта'),
        content: const Text('Вы уверены, что хотите выйти из аккаунта?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Отмена'),
          ),
          TextButton(
            onPressed: () async {
              Navigator.of(context).pop();
              await _handleLogout(ref);
            
            },
            child: Text(
              'Выйти',
              style: TextStyle(
                color: Theme.of(context).colorScheme.error,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
