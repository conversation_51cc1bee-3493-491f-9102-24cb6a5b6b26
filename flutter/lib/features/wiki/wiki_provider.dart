import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:luxury_app/app_providers.dart';
import 'package:luxury_app/core/models/data_state.dart';
import 'package:luxury_app/core/services/cache_service.dart';
import 'package:luxury_app/core/services/connectivity_service.dart';
import 'package:luxury_app/features/wiki/wiki_data/wiki_models.dart';
import 'package:luxury_app/features/wiki/wiki_data/wiki_repository.dart';

/// Провайдер для WikiNotifier
final wikiProvider = StateNotifierProvider<WikiNotifier, WikiState>((ref) {
  return WikiNotifier(ref.read(wikiRepositoryProvider));
});

/// Состояние Wiki
class WikiState {
  final List<WikiFolder> folders;
  final bool isLoading;
  final String? error;
  final DataSource source;

  // Дополнительные свойства для совместимости
  final Set<String> expandedFolders;
  final bool allFoldersExpanded;
  final bool isSearching;
  final String? loadedFileId;
  final Map<String, dynamic>? fileContent;

  const WikiState({
    this.folders = const [],
    this.isLoading = false,
    this.error,
    this.source = DataSource.cache,
    this.expandedFolders = const {},
    this.allFoldersExpanded = false,
    this.isSearching = false,
    this.loadedFileId,
    this.fileContent,
  });

  WikiState copyWith({
    List<WikiFolder>? folders,
    bool? isLoading,
    String? error,
    DataSource? source,
    Set<String>? expandedFolders,
    bool? allFoldersExpanded,
    bool? isSearching,
    String? loadedFileId,
    Map<String, dynamic>? fileContent,
  }) {
    return WikiState(
      folders: folders ?? this.folders,
      isLoading: isLoading ?? this.isLoading,
      error: error,
      source: source ?? this.source,
      expandedFolders: expandedFolders ?? this.expandedFolders,
      allFoldersExpanded: allFoldersExpanded ?? this.allFoldersExpanded,
      isSearching: isSearching ?? this.isSearching,
      loadedFileId: loadedFileId ?? this.loadedFileId,
      fileContent: fileContent ?? this.fileContent,
    );
  }

  // Вспомогательные геттеры для совместимости
  bool get hasFileLoaded => loadedFileId != null && fileContent != null;
  bool get hasError => error != null;
  bool get hasAudio => fileContent?['audio_url'] != null;
  String? get audioUrl => fileContent?['audio_url'] as String?;
}

/// Notifier для управления состоянием Wiki
class WikiNotifier extends StateNotifier<WikiState> {
  final WikiRepository _wikiRepository;
  StreamSubscription<List<WikiFolder>>? _foldersSubscription;
  StreamSubscription<List<WikiFolder>>? _filesSubscription;
  StreamSubscription<bool>? _connectivitySubscription;
  StreamSubscription<bool>? _cacheClearedSubscription;

  bool get isActive => mounted;

  WikiNotifier(this._wikiRepository) : super(const WikiState()) {
    _initializeWiki();
    _subscribeToCacheCleared();
  }

  @override
  void dispose() {
    _foldersSubscription?.cancel();
    _filesSubscription?.cancel();
    _connectivitySubscription?.cancel();
    _cacheClearedSubscription?.cancel();
    super.dispose();
  }

  /// Подписка на очистку кэша
  void _subscribeToCacheCleared() {
    _cacheClearedSubscription = CacheService.instance.cacheClearedStream.listen(
      (_) {
        if (!isActive) return;
        debugPrint('🔄 WikiNotifier: Кэш очищен, сбрасываем состояние');

        // Сбрасываем состояние до начального
        state = const WikiState();

        // Перезагружаем данные
        _initializeWiki();
      },
    );
  }

  /// Инициализация Wiki
  Future<void> _initializeWiki() async {
    if (!isActive) return;

    debugPrint('🔄 WikiNotifier: Инициализация Wiki...');

    // Сначала загружаем из кэша
    await _loadFromCache();

    // Подписываемся на изменения состояния подключения
    _subscribeToConnectivity();
  }

  /// Загрузка данных из кэша
  Future<void> _loadFromCache() async {
    if (!isActive) return;

    try {
      debugPrint('📱 WikiNotifier: Загружаем Wiki из кэша...');
      state = state.copyWith(isLoading: true, error: null);

      final folders = await _wikiRepository.getFolderHierarchy();

      if (!isActive) return;

      state = state.copyWith(
        folders: folders,
        isLoading: false,
        source: DataSource.cache,
      );

      debugPrint('✅ WikiNotifier: Загружено ${folders.length} папок из кэша');
    } catch (e) {
      if (!isActive) return;

      debugPrint('❌ WikiNotifier: Ошибка загрузки из кэша: $e');
      state = state.copyWith(
        isLoading: false,
        error: 'Ошибка загрузки данных из кэша',
      );
    }
  }

  /// Подписка на изменения состояния подключения
  void _subscribeToConnectivity() {
    _connectivitySubscription = ConnectivityService
        .instance
        .supabaseStatusStream
        .listen((isConnected) {
          if (!isActive) return;

          if (isConnected) {
            debugPrint(
              '🌐 WikiNotifier: Подключение восстановлено - обновляем данные',
            );
            // Принудительно обновляем данные при подключении
            loadFolderHierarchy();
          } else {
            debugPrint(
              '📱 WikiNotifier: Подключение потеряно - работаем с кэшем',
            );
          }
        });

    // Подписываемся на real-time обновления
    _subscribeToWiki();
  }

  /// Подписка на real-time обновления
  void _subscribeToWiki() {
    if (!isActive) return;

    debugPrint('🔄 WikiNotifier: Подписываемся на real-time обновления Wiki');

    try {
      // Подписка на изменения папок
      _foldersSubscription = _wikiRepository.subscribeToFolderHierarchy().listen(
        (folders) {
          if (!isActive) return;
          debugPrint(
            '✅ WikiNotifier: Получено ${folders.length} папок через real-time',
          );
          state = state.copyWith(
            folders: folders,
            isLoading: false,
            source: DataSource.network,
          );
        },
        onError: (error) {
          if (!isActive) return;
          debugPrint(
            '❌ WikiNotifier: Ошибка real-time подписки на папки: $error',
          );
          // Не показываем ошибку пользователю - данные из кэша уже загружены
        },
      );

      // Подписка на изменения файлов
      _filesSubscription = _wikiRepository.subscribeToFileUpdates().listen(
        (folders) {
          if (!isActive) return;
          debugPrint('🔄 WikiNotifier: Обновления файлов через real-time');
          state = state.copyWith(folders: folders, source: DataSource.network);
        },
        onError: (error) {
          if (!isActive) return;
          debugPrint(
            '❌ WikiNotifier: Ошибка real-time подписки на файлы: $error',
          );
          // Не показываем ошибку пользователю - данные из кэша уже загружены
        },
      );
    } catch (e) {
      debugPrint('❌ WikiNotifier: Ошибка создания подписок: $e');
      // Не показываем ошибку пользователю - данные из кэша уже загружены
    }
  }

  /// Загружает иерархию папок (для совместимости и принудительного обновления)
  Future<void> loadFolderHierarchy() async {
    if (!isActive) return;

    debugPrint('🔄 WikiNotifier: Принудительная загрузка Wiki иерархии...');
    state = state.copyWith(isLoading: true, error: null);

    try {
      final folders = await _wikiRepository.getFolderHierarchy();

      if (!isActive) return;

      state = state.copyWith(
        folders: folders,
        isLoading: false,
        source: DataSource.network,
      );

      debugPrint('✅ WikiNotifier: Загружено ${folders.length} папок');
    } catch (e) {
      if (!isActive) return;

      debugPrint('❌ WikiNotifier: Ошибка загрузки Wiki иерархии: $e');
      state = state.copyWith(isLoading: false, error: 'Ошибка загрузки данных');
    }
  }

  /// Алиас для loadFolderHierarchy (для совместимости)
  Future<void> loadFolders() => loadFolderHierarchy();

  /// Загружает содержимое файла
  Future<void> loadFile(String fileId) async {
    if (!isActive) return;

    debugPrint('🔄 WikiNotifier: Загружаем содержимое файла $fileId...');

    try {
      state = state.copyWith(isLoading: true, error: null);

      final content = await _wikiRepository.getFileContent(fileId);

      if (!isActive) return;

      state = state.copyWith(
        loadedFileId: fileId,
        fileContent: content,
        isLoading: false,
      );

      debugPrint('✅ WikiNotifier: Загружено содержимое файла $fileId');
    } catch (e) {
      if (!isActive) return;

      debugPrint('❌ WikiNotifier: Ошибка загрузки файла $fileId: $e');
      state = state.copyWith(isLoading: false, error: 'Ошибка загрузки файла');
    }
  }

  /// Получает содержимое файла (для совместимости)
  Future<Map<String, dynamic>> getFileContent(
    String fileId, {
    bool forceRefresh = false,
  }) async {
    debugPrint('🔄 WikiNotifier: Загружаем содержимое файла $fileId...');

    try {
      final content = await _wikiRepository.getFileContent(
        fileId,
        forceRefresh: forceRefresh,
      );

      debugPrint('✅ WikiNotifier: Загружено содержимое файла $fileId');
      return content;
    } catch (e) {
      debugPrint('❌ WikiNotifier: Ошибка загрузки файла $fileId: $e');
      rethrow;
    }
  }

  /// Поиск в Wiki
  Future<void> searchWiki(String query) async {
    state = state.copyWith(isSearching: query.isNotEmpty, isLoading: true);
    debugPrint('🔍 WikiNotifier: Поиск: "$query"');
    try {
      final folders = await _wikiRepository.searchWiki(query);
      state = state.copyWith(
        folders: folders,
        isLoading: false,
        error: null,
      );
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: 'Ошибка поиска: $e',
      );
    }
  }

  /// Переключение всех папок (заглушка для совместимости)
  void toggleAllFolders() {
    final newExpanded = !state.allFoldersExpanded;
    final expandedFolders =
        newExpanded ? state.folders.map((f) => f.id).toSet() : <String>{};

    state = state.copyWith(
      allFoldersExpanded: newExpanded,
      expandedFolders: expandedFolders,
    );

    debugPrint(
      '📁 WikiNotifier: Все папки ${newExpanded ? "развернуты" : "свернуты"}',
    );
  }

  /// Переключение конкретной папки (заглушка для совместимости)
  void toggleFolder(String folderId) {
    final expandedFolders = Set<String>.from(state.expandedFolders);

    if (expandedFolders.contains(folderId)) {
      expandedFolders.remove(folderId);
    } else {
      expandedFolders.add(folderId);
    }

    state = state.copyWith(
      expandedFolders: expandedFolders,
      allFoldersExpanded: expandedFolders.length == state.folders.length,
    );

    debugPrint('📁 WikiNotifier: Папка $folderId переключена');
  }
}
