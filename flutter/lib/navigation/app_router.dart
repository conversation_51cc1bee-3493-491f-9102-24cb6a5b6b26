import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:luxury_app/app_provider.dart';
import 'package:luxury_app/app_shell.dart';
import 'package:luxury_app/app_state.dart';
import 'package:luxury_app/features/ai_chat/ai_chat_view/ai_chat_screen.dart';
import 'package:luxury_app/features/auth/auth_screen.dart';
import 'package:luxury_app/features/news/news_content.dart';
import 'package:luxury_app/features/settings/settings_screen.dart';
import 'package:luxury_app/features/settings/auth/view/reset_password_screen.dart';
import 'package:luxury_app/features/settings/auth/auth_provider.dart';
import 'package:luxury_app/features/wiki/wiki_view/wiki_screen.dart';
import 'package:luxury_app/navigation/route_config.dart';

class AppRouter {
  static GoRouter? _routerInstance;
  static final _rootNavigatorKey = GlobalKey<NavigatorState>();

  static GoRouter get router {
    _routerInstance ??= GoRouter(
      navigatorKey: _rootNavigatorKey,
      initialLocation: '/news',
      routes: _createRoutes(),
      errorBuilder: (context, state) => const ErrorScreen(),
      redirect: _handleNavigation,
    );
    return _routerInstance!;
  }

  static String? _handleNavigation(BuildContext context, GoRouterState state) {
    final container = ProviderScope.containerOf(context);
    final authState = container.read(authProvider);
    final isAuthRoute = state.matchedLocation == '/auth';

    // Если состояние авторизации ещё загружается, не делаем редиректов
    if (authState.isLoading) {
      return null;
    }

    if (!authState.isAuthenticated && !isAuthRoute) {
      return '/auth';
    }
    if (authState.isAuthenticated && isAuthRoute) {
      return '/news';
    }
    return null;
  }

  static List<RouteBase> _createRoutes() {
    return [
      
      GoRoute(
        path: '/auth',
        name: 'auth',
        pageBuilder: (context, state) => _buildPage(
          key: state.pageKey,
          child: const AuthScreen(),
        ),
      ),
      ShellRoute(
        navigatorKey: GlobalKey<NavigatorState>(),
        builder: (context, state, child) => AppShell(body: child),
        routes: [
          GoRoute(
            path: RouteConfig.newsPath == '/' ? '/news' : RouteConfig.newsPath,
            name: RouteConfig.newsRoute,
            pageBuilder: (context, state) {
              _updateAppState(context, DrawerMode.news, screenTitle: 'Новости');
              return _buildPage(key: state.pageKey, child: const NewsContent());
            },
          ),
          GoRoute(
            path: RouteConfig.wikiPath,
            name: RouteConfig.wikiRoute,
            pageBuilder: (context, state) {
              final pageId = state.pathParameters['pageId']!;
              final fileName = state.uri.queryParameters['name'];

              // Обновляем состояние для wiki
              _updateAppState(
                context,
                DrawerMode.wiki,
                pageId: pageId,
                screenTitle: fileName ?? 'База знаний',
              );

              return _buildPage(
                key: state.pageKey,
                child: WikiContent(fileId: pageId, fileName: fileName),
              );
            },
          ),
          GoRoute(
            path: RouteConfig.aiPath,
            name: RouteConfig.aiRoute,
            pageBuilder: (context, state) {
              final chatIdString = state.pathParameters['chatId']!;
              final chatId = int.parse(chatIdString);

              // Обновляем состояние для AI чата
              _updateAppState(
                context,
                DrawerMode.chat,
                assistantId: chatIdString,
                screenTitle: 'ИИ-ассистент',
              );

              // Добавляем отладочный лог
              debugPrint('🏗️ [AppRouter] Создаем страницу для чата $chatId');

              return _buildPage(
                key: state.pageKey,
                child: AIChatContent(chatId: chatId),
              );
            },
          ),

          GoRoute(
            path: 'reset-password',
            name: 'resetPassword',
            pageBuilder: (context, state) {
              final token = state.uri.queryParameters['token'] ?? '';
              final email = state.uri.queryParameters['email'] ?? '';
              return _buildPage(
                key: state.pageKey,
                child: ResetPasswordScreen(token: token, email: email),
              );
            },
          ),
          GoRoute(
            path: RouteConfig.settingsPath,
            name: RouteConfig.settingsRoute,
            pageBuilder: (context, state) {
              // Обновляем состояние для настроек
              _updateAppState(
                context,
                DrawerMode.news,
                screenTitle: 'Настройки',
              );

              return _buildPage(
                key: state.pageKey,
                child: const SettingsScreen(),
              );
            },
          ),
        ],
      ),
    ];
  }

  /// Обновляет состояние приложения при переходе на новую страницу
  static void _updateAppState(
    BuildContext context,
    DrawerMode mode, {
    String? pageId,
    String? assistantId,
    String? screenTitle,
  }) {
    // Используем WidgetsBinding для отложенного выполнения
    WidgetsBinding.instance.addPostFrameCallback((_) {
      try {
        final container = ProviderScope.containerOf(context);
        final notifier = container.read(appProvider.notifier);

        notifier.navigateToScreen(
          mode,
          pageId: pageId,
          assistantId: assistantId,
        );

        // Устанавливаем заголовок экрана, если он передан
        if (screenTitle != null) {
          notifier.setScreenTitle(screenTitle);
        }

        if (kDebugMode) {
          debugPrint(
            '🔄 [AppRouter] Обновили состояние: mode=$mode, pageId=$pageId, assistantId=$assistantId, title=$screenTitle',
          );
        }
      } catch (e) {
        if (kDebugMode) {
          debugPrint('❌ [AppRouter] Ошибка обновления состояния: $e');
        }
      }
    });
  }

  static Page<void> _buildPage({required LocalKey key, required Widget child}) {
    return CustomTransitionPage<void>(
      key: key,
      child: child,
      transitionsBuilder: (context, animation, secondaryAnimation, child) {
        return FadeTransition(opacity: animation, child: child);
      },
    );
  }
}

class ErrorScreen extends StatelessWidget {
  const ErrorScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Ошибка')),
      body: const Center(child: Text('Страница не найдена')),
    );
  }
}
